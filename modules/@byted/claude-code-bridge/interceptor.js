const fs = require("fs");

const originalFetch = globalThis.fetch;

// const base_url = process.env.ANTHROPIC_API_URL;
const base_url = "https://ark-cn-beijing.bytedance.net/api/v3/chat/completions";
// const api_key = process.env.ANTHROPIC_AUTH_TOKEN;
const api_key = "e9bd9096-e27c-402c-8df9-16aa4e8fc417";
// const model = process.env.ANTHROPIC_MODEL;
const model = "ep-20250327151331-hfrk5"
// const max_tokens = process.env.ANTHROPIC_MAX_TOKENS;
const max_tokens = 8192;

const logfile = '/Library/workspace/claude-code-bd/log.log'

process.getuid = () => 1000;

function writeLog(key, message) {
    if (logfile) {
        const log_file = logfile;
        fs.appendFileSync(log_file, `[${key}]: ${message}\n`, "utf-8");
    }
}

writeLog("App", "start");

globalThis.fetch = async function (...args) {
    if (args[0].includes("statsig.anthropic.com/v1/rgstr")) {
        return Response.json({
            ok: true
        });
    }
    let isInterceptor = false;
    let isStream = false;
    if (args[0].includes("v1/messages")) {
        isInterceptor = true;
        args[0] = `${base_url}?ak=${api_key}`;
        writeLog("request", args[0]);
        args[1].headers.set("Authorization", "Bearer " + api_key);
        let query = JSON.parse(args[1].body);
        writeLog(
            "headers",
            JSON.stringify(Object.fromEntries(args[1].headers.entries()), null, 4)
        );
        writeLog("query", JSON.stringify(query, null, 4));
        query.model = model;
        if (max_tokens != undefined) {
            query.max_tokens = parseInt(max_tokens);
        }
        if (query.stream) {
            isStream = true;
        }
        query = convertQueryClaudeToGPT(query);

        args[1].body = JSON.stringify(query);
    }
    const res = await originalFetch.apply(this, args);
    if (!res.ok) {
        writeLog("error", `${res.status} ${await res.clone().text()}`);
        return res;
    }
    if (isInterceptor) {
        if (isStream) {
            const stream = transformStream(res, (data) => {
                // process.stdout.write(data.choices[0].delta.content);
                writeLog("stream content", data.choices[0].delta.content);
                return convertResponseChunkGPTToClaude(data);
            });
            return stream;
        } else {
            let json = await res.json();
            writeLog("response", JSON.stringify(json, null, 4));
            json = convertResponseGPTToClaude(json);
            return Response.json(json);
        }
    }
    return res;
};

function transformStream(response, transformChunk) {
    const reader = response.body.getReader();
    const decoder = new TextDecoder();
    const stream = new ReadableStream({
        async start(controller) {
            while (true) {
                const {
                    done,
                    value
                } = await reader.read();
                if (done) {
                    break;
                }
                const chunk = decoder.decode(value);
                if (chunk.includes("DONE")) {
                    controller.enqueue(chunk);
                    break;
                }
                const chunkData = JSON.parse(chunk.split("data: ")[1]);
                const transformedChunk = await transformChunk(chunkData);
                controller.enqueue(`data: ${JSON.stringify(transformedChunk)}`);
            }
            controller.close();
        },
    });
    return new Response(stream, {
        headers: response.headers,
        status: response.status,
    });
}

function convertQueryClaudeToGPT(claudeRequest) {
    const {
        model,
        max_tokens,
        messages,
        temperature,
        stream,
        tools
    } =
        claudeRequest;

    // 转换消息格式
    const gptMessages = messages
        .map((msg) => {
            const roleMap = {
                user: "user",
                assistant: "assistant",
                system: "system",
                tool: "tool", // GPT也使用'tool'角色
            };

            //处理cache_control
            if (Array.isArray(msg.content)) {
                msg.content.forEach((it) => {
                    delete it.cache_control;
                });
            }

            const convertedMsg = {
                role: roleMap[msg.role] || msg.role,
                content: msg.content,
            };




            const convertedMsgs = [];
            let removeMsg = false;
            //处理tool_use
            if (msg.role === "assistant" && Array.isArray(msg.content)) {
                const tool_use = msg.content.filter((item) => item.type === "tool_use");
                convertedMsg.content = msg.content.filter(
                    (item) => item.type !== "tool_use"
                );
                if (convertedMsg.content.length === 0) {
                    removeMsg = true;
                }
                const tool_calls = tool_use.map((item) => ({
                    id: item.id,
                    type: "function",
                    function: {
                        name: item.name,
                        arguments: JSON.stringify(item.input),
                    },
                }));
                convertedMsg.tool_calls = tool_calls;
            }

            //处理tool_result
            if (msg.role == "user" && Array.isArray(msg.content)) {
                const tool_result = msg.content.filter(
                    (item) => item.type === "tool_result"
                );
                convertedMsg.content = msg.content.filter(
                    (item) => item.type !== "tool_result"
                );
                convertedMsg.content.forEach((it) => {
                    delete it.cache_control;
                });
                if (convertedMsg.content.length === 0) {
                    removeMsg = true;
                }
                if (tool_result.length > 0) {
                    convertedMsgs.push(
                        ...tool_result.map((it) => {
                            return {
                                role: "tool",
                                content: it.content,
                                tool_call_id: it.tool_use_id,
                            };
                        })
                    );
                }
            }
            if (!removeMsg) {
                convertedMsgs.unshift(convertedMsg);
            }

            //处理单个数组且为text类型
            if (convertedMsg.content.length === 1 && convertedMsg.content[0].type === 'text') {
                convertedMsg.content = convertedMsg.content[0].text;
            }

            return convertedMsgs;
        })
        .flat();

    // 处理系统提示

    claudeRequest.system ?.reverse().forEach((item) => {
        if (item.type === "text") {
            gptMessages.unshift({
                role: "system",
                content: item.text,
            });
        }
    });

    // 转换工具定义
    let gptTools;
    if (tools) {
        gptTools = tools.map((tool) => {
            delete tool.input_schema.$schema;
            const parameters = tool.input_schema || tool.parameters;
            if (parameters ?.properties) {
                Object.keys(parameters.properties).forEach((key) => {
                    delete parameters.properties[key]['examples']
                });
            }
            return {
                type: "function",
                function: {
                    name: tool.name,
                    description: tool.description,
                    parameters,
                },
            };
        });
    }

    // 构建GPT请求
    const gptRequest = {
        model: model,
        messages: gptMessages,
        max_tokens: max_tokens,
        temperature: temperature,
        stream: stream,
    };

    // 添加工具相关字段
    if (gptTools) {
        gptRequest.tools = gptTools;
    }

    // 添加元数据
    if (claudeRequest.metadata) {
        gptRequest.user = claudeRequest.metadata.user_id;
    }

    return gptRequest;
}

function convertResponseGPTToClaude(gptResponse) {
    const choice = gptResponse.choices ?. [0];
    const message = choice ?.message || {};
    const tool_calls = gptResponse.choices ?.find((it) => it.message.tool_calls) ?.message.tool_calls;

    const finish_reason_map = {
        stop: "end_turn",
        length: "max_tokens",
        function_call: "tool_call",
        content_filter: "content_blocked",
        tool_calls: "tool_use",
    };

    const claudeResponse = {
        type: "message",
        content: [{
            type: "text",
            text: message.content,
        },
        ...(tool_calls ?.length > 0 ?
            convertToolsResponseGPTToClaude(tool_calls) : []),
        ],
        id: gptResponse.id || `msg_${Date.now()}`,
        model,
        role: "assistant",
        stop_reason: finish_reason_map[choice ?.finish_reason] || "end_turn",
        usage: {
            input_tokens: gptResponse.usage ?.prompt_tokens || 0,
            output_tokens: gptResponse.usage ?.completion_tokens || 0,
        },
    };

    return claudeResponse;
}

function convertToolsResponseGPTToClaude(gptTools) {
    if (!gptTools || !Array.isArray(gptTools)) return [];

    return gptTools.map((tool) => ({
        type: "tool_use",
        name: tool.function ?.name || "",
        input: typeof tool.function ?.arguments === "string" ?
            JSON.parse(tool.function.arguments) :
            tool.function ?.arguments || {},
        id: tool.id,
    }));
}

function convertResponseChunkGPTToClaude(gptChunk) {
    const choice = gptChunk.choices ?. [0];
    const delta = choice ?.delta || {};
    const isToolCall = delta.tool_calls && delta.tool_calls.length > 0;

    // 初始消息事件
    if (!delta.content && !delta.role && !isToolCall) {
        return {
            type: "message_start",
            message: {
                id: gptChunk.id || `msg_${Date.now()}`,
                type: "message",
                role: "assistant",
                content: [],
                model,
            },
        };
    }

    // 工具调用事件
    if (isToolCall) {
        const toolCall = delta.tool_calls[0];

        // 工具调用开始
        if (toolCall.index !== undefined && !toolCall.id) {
            return {
                type: "content_block_start",
                index: toolCall.index,
                content_block: {
                    type: "tool_use",
                    tool_use_id: `toolu_${Date.now()}${Math.random()
                        .toString(16)
                        .substr(2, 8)}`,
                    name: toolCall.function ?.name || "",
                },
            };
        }

        // 工具参数增量
        if (toolCall.function ?.arguments) {
            return {
                type: "content_block_delta",
                index: toolCall.index,
                delta: {
                    type: "input_json_delta",
                    partial_json: toolCall.function.arguments,
                },
            };
        }
    }

    // 文本内容增量
    if (delta.content) {
        return {
            type: "content_block_delta",
            index: 0,
            delta: {
                type: "text_delta",
                text: delta.content,
            },
        };
    }

    // 结束事件
    if (choice ?.finish_reason) {
        return {
            type: "message_stop",
            "amazon-bedrock-invocationMetrics": {
                inputTokenCount: 0,
                outputTokenCount: 0,
                invocationLatency: 0,
                firstByteLatency: 0,
            },
        };
    }

    // 默认返回心跳事件
    return {
        type: "ping"
    };
}