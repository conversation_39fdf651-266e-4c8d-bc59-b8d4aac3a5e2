{"name": "claude-code-bd", "lockfileVersion": 3, "requires": true, "packages": {"node_modules/@anthropic-ai/claude-code": {"version": "1.0.67", "resolved": "https://bnpm.byted.org/@anthropic-ai/claude-code/-/claude-code-1.0.67.tgz", "integrity": "sha512-r7CfhbKBXgaL5Wo0BIh08SOahFYQPcbHCNnMLtse7iDd2IVBWeOxqoiqQvzRQ0wTCiqLTshRbnmLWHyP4AbuyQ==", "license": "SEE LICENSE IN README.md", "bin": {"claude": "cli.js"}, "engines": {"node": ">=18.0.0"}, "optionalDependencies": {"@img/sharp-darwin-arm64": "^0.33.5", "@img/sharp-darwin-x64": "^0.33.5", "@img/sharp-linux-arm": "^0.33.5", "@img/sharp-linux-arm64": "^0.33.5", "@img/sharp-linux-x64": "^0.33.5", "@img/sharp-win32-x64": "^0.33.5"}}, "node_modules/@byted/claude-code-bridge": {"version": "1.0.11", "resolved": "https://bnpm.byted.org/@byted/claude-code-bridge/-/claude-code-bridge-1.0.11.tgz", "integrity": "sha512-dr2VnqRjWeFstDyTWrucywlNQIThlD50TfhwzFF/ktWV7gEP4+l0HCx9ixWjh2bazov+1W5AGAidta5psUnKTg==", "license": "ISC", "dependencies": {"@anthropic-ai/claude-code": "*"}, "bin": {"claude-code": "bin/claude-code"}}, "node_modules/@img/sharp-darwin-arm64": {"version": "0.33.5", "resolved": "https://bnpm.byted.org/@img/sharp-darwin-arm64/-/sharp-darwin-arm64-0.33.5.tgz", "integrity": "sha512-UT4p+iz/2H4twwAoLCqfA9UH5pI6DggwKEGuaPy7nCVQ8ZsiY5PIcrRvD1DzuY3qYL07NtIQcWnBSY/heikIFQ==", "cpu": ["arm64"], "license": "Apache-2.0", "optional": true, "os": ["darwin"], "engines": {"node": "^18.17.0 || ^20.3.0 || >=21.0.0"}, "funding": {"url": "https://opencollective.com/libvips"}, "optionalDependencies": {"@img/sharp-libvips-darwin-arm64": "1.0.4"}}, "node_modules/@img/sharp-libvips-darwin-arm64": {"version": "1.0.4", "resolved": "https://bnpm.byted.org/@img/sharp-libvips-darwin-arm64/-/sharp-libvips-darwin-arm64-1.0.4.tgz", "integrity": "sha512-XblONe153h0O2zuFfTAbQYAX2JhYmDHeWikp1LM9Hul9gVPjFY427k6dFEcOL72O01QxQsWi761svJ/ev9xEDg==", "cpu": ["arm64"], "license": "LGPL-3.0-or-later", "optional": true, "os": ["darwin"], "funding": {"url": "https://opencollective.com/libvips"}}}}