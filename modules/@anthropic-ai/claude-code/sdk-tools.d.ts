/* eslint-disable */
/**
 * This file was automatically generated by json-schema-to-typescript.
 * DO NOT MODIFY IT BY HAND. Instead, modify the source JSONSchema file,
 * and run json-schema-to-typescript to regenerate this file.
 */

/**
 * JSON Schema definitions for Claude CLI tool inputs
 */
export type ToolInputSchemas =
  | AgentInput
  | BashInput
  | BashOutputInput
  | ExitPlanModeInput
  | FileEditInput
  | FileMultiEditInput
  | FileReadInput
  | FileWriteInput
  | GlobInput
  | GrepInput
  | KillShellInput
  | ListMcpResourcesInput
  | LsInput
  | McpInput
  | NotebookEditInput
  | NotebookReadInput
  | ReadMcpResourceInput
  | TodoWriteInput
  | WebFetchInput
  | WebSearchInput;

export interface AgentInput {
  /**
   * A short (3-5 word) description of the task
   */
  description: string;
  /**
   * The task for the agent to perform
   */
  prompt: string;
  /**
   * The type of specialized agent to use for this task
   */
  subagent_type: string;
}
export interface BashInput {
  /**
   * The command to execute
   */
  command: string;
  /**
   * Optional timeout in milliseconds (max 600000)
   */
  timeout?: number;
  /**
   *  Clear, concise description of what this command does in 5-10 words. Examples:
   * Input: ls
   * Output: Lists files in current directory
   *
   * Input: git status
   * Output: Shows working tree status
   *
   * Input: npm install
   * Output: Installs package dependencies
   *
   * Input: mkdir foo
   * Output: Creates directory 'foo'
   */
  description?: string;
  /**
   * whether to run this command in sandboxed mode: command run in this mode may not write to the filesystem or use the network, but they can read files, analyze data, and report back to you.  When possible, run commands (e.g. grep) in this mode to present a smoother experience for the human, who isn't prompted to approve commands run in sandbox mode. If you run a command in sandbox mode and it looks like it fails because it needs write access after all, try again in non-sandbox mode
   */
  sandbox?: boolean;
  /**
   * Optional shell path to use instead of the default shell. The snapshot path will be set to undefined as well. Used primarily for testing.
   */
  shellExecutable?: string;
}
export interface BashOutputInput {
  /**
   * The ID of the background shell to retrieve output from
   */
  shell_id: string;
}
export interface ExitPlanModeInput {
  /**
   * The plan you came up with, that you want to run by the user for approval. Supports markdown. The plan should be pretty concise.
   */
  plan: string;
}
export interface FileEditInput {
  /**
   * The absolute path to the file to modify
   */
  file_path: string;
  /**
   * The text to replace
   */
  old_string: string;
  /**
   * The text to replace it with (must be different from old_string)
   */
  new_string: string;
  /**
   * Replace all occurences of old_string (default false)
   */
  replace_all?: boolean;
}
export interface FileMultiEditInput {
  /**
   * The absolute path to the file to modify
   */
  file_path: string;
  /**
   * Array of edit operations to perform sequentially on the file
   *
   * @minItems 1
   */
  edits: [
    {
      /**
       * The text to replace
       */
      old_string: string;
      /**
       * The text to replace it with
       */
      new_string: string;
      /**
       * Replace all occurences of old_string (default false).
       */
      replace_all?: boolean;
    },
    ...{
      /**
       * The text to replace
       */
      old_string: string;
      /**
       * The text to replace it with
       */
      new_string: string;
      /**
       * Replace all occurences of old_string (default false).
       */
      replace_all?: boolean;
    }[]
  ];
}
export interface FileReadInput {
  /**
   * The absolute path to the file to read
   */
  file_path: string;
  /**
   * The line number to start reading from. Only provide if the file is too large to read at once
   */
  offset?: number;
  /**
   * The number of lines to read. Only provide if the file is too large to read at once.
   */
  limit?: number;
}
export interface FileWriteInput {
  /**
   * The absolute path to the file to write (must be absolute, not relative)
   */
  file_path: string;
  /**
   * The content to write to the file
   */
  content: string;
}
export interface GlobInput {
  /**
   * The glob pattern to match files against
   */
  pattern: string;
  /**
   * The directory to search in. If not specified, the current working directory will be used. IMPORTANT: Omit this field to use the default directory. DO NOT enter "undefined" or "null" - simply omit it for the default behavior. Must be a valid directory path if provided.
   */
  path?: string;
}
export interface GrepInput {
  /**
   * The regular expression pattern to search for in file contents
   */
  pattern: string;
  /**
   * File or directory to search in (rg PATH). Defaults to current working directory.
   */
  path?: string;
  /**
   * Glob pattern to filter files (e.g. "*.js", "*.{ts,tsx}") - maps to rg --glob
   */
  glob?: string;
  /**
   * Output mode: "content" shows matching lines (supports -A/-B/-C context, -n line numbers, head_limit), "files_with_matches" shows file paths (supports head_limit), "count" shows match counts (supports head_limit). Defaults to "files_with_matches".
   */
  output_mode?: "content" | "files_with_matches" | "count";
  /**
   * Number of lines to show before each match (rg -B). Requires output_mode: "content", ignored otherwise.
   */
  "-B"?: number;
  /**
   * Number of lines to show after each match (rg -A). Requires output_mode: "content", ignored otherwise.
   */
  "-A"?: number;
  /**
   * Number of lines to show before and after each match (rg -C). Requires output_mode: "content", ignored otherwise.
   */
  "-C"?: number;
  /**
   * Show line numbers in output (rg -n). Requires output_mode: "content", ignored otherwise.
   */
  "-n"?: boolean;
  /**
   * Case insensitive search (rg -i)
   */
  "-i"?: boolean;
  /**
   * File type to search (rg --type). Common types: js, py, rust, go, java, etc. More efficient than include for standard file types.
   */
  type?: string;
  /**
   * Limit output to first N lines/entries, equivalent to "| head -N". Works across all output modes: content (limits output lines), files_with_matches (limits file paths), count (limits count entries). When unspecified, shows all results from ripgrep.
   */
  head_limit?: number;
  /**
   * Enable multiline mode where . matches newlines and patterns can span lines (rg -U --multiline-dotall). Default: false.
   */
  multiline?: boolean;
}
export interface KillShellInput {
  /**
   * The ID of the background shell to kill
   */
  shell_id: string;
}
export interface ListMcpResourcesInput {
  /**
   * Optional server name to filter resources by
   */
  server?: string;
}
export interface LsInput {
  /**
   * The absolute path to the directory to list (must be absolute, not relative)
   */
  path: string;
  /**
   * List of glob patterns to ignore
   */
  ignore?: string[];
}
export interface McpInput {
  [k: string]: unknown;
}
export interface NotebookEditInput {
  /**
   * The absolute path to the Jupyter notebook file to edit (must be absolute, not relative)
   */
  notebook_path: string;
  /**
   * The ID of the cell to edit. When inserting a new cell, the new cell will be inserted after the cell with this ID, or at the beginning if not specified.
   */
  cell_id?: string;
  /**
   * The new source for the cell
   */
  new_source: string;
  /**
   * The type of the cell (code or markdown). If not specified, it defaults to the current cell type. If using edit_mode=insert, this is required.
   */
  cell_type?: "code" | "markdown";
  /**
   * The type of edit to make (replace, insert, delete). Defaults to replace.
   */
  edit_mode?: "replace" | "insert" | "delete";
}
export interface NotebookReadInput {
  /**
   * The absolute path to the Jupyter notebook file to read (must be absolute, not relative)
   */
  notebook_path: string;
  /**
   * The ID of a specific cell to read. If not provided, all cells will be read.
   */
  cell_id?: string;
}
export interface ReadMcpResourceInput {
  /**
   * The MCP server name
   */
  server: string;
  /**
   * The resource URI to read
   */
  uri: string;
}
export interface TodoWriteInput {
  /**
   * The updated todo list
   */
  todos: {
    content: string;
    status: "pending" | "in_progress" | "completed";
    priority: "high" | "medium" | "low";
    id: string;
  }[];
}
export interface WebFetchInput {
  /**
   * The URL to fetch content from
   */
  url: string;
  /**
   * The prompt to run on the fetched content
   */
  prompt: string;
}
export interface WebSearchInput {
  /**
   * The search query to use
   */
  query: string;
  /**
   * Only include search results from these domains
   */
  allowed_domains?: string[];
  /**
   * Never include search results from these domains
   */
  blocked_domains?: string[];
}
